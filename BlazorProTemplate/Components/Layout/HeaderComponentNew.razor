@using Microsoft.AspNetCore.Components

<header class="header">
    <div class="header-container">
        <!-- Logo Section -->
        <div class="header-logo">
            <div class="logo-circle">
                <span class="logo-text">@LogoText</span>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="header-nav">
            @foreach (var item in NavigationItems)
            {
                <a href="@item.Url" 
                   class="nav-item @(item.IsActive ? "active" : "")"
                   @onclick="() => OnNavigationClick(item)">
                    
                    @if (!string.IsNullOrEmpty(item.Icon))
                    {
                        <i class="@item.Icon nav-icon"></i>
                    }
                    
                    <span class="nav-text">@item.Text</span>
                </a>
            }
        </nav>

        <!-- User Section -->
        <div class="header-user">
            <!-- Dropdown -->
            <div class="user-dropdown">
                <button class="dropdown-toggle" @onclick="ToggleDropdown">
                    <span class="dropdown-text">@DropdownText</span>
                    <i class="dropdown-arrow">▼</i>
                </button>
                
                @if (IsDropdownOpen)
                {
                    <div class="dropdown-menu">
                        @foreach (var option in DropdownOptions)
                        {
                            <a href="#" class="dropdown-item" @onclick="() => OnDropdownSelect(option)">
                                @option
                            </a>
                        }
                    </div>
                }
            </div>

            <!-- User Profile -->
            <div class="user-profile">
                <span class="user-name">@UserName</span>
                <i class="user-icon">👤</i>
            </div>

            <!-- Time -->
            <div class="header-time">
                <span class="time-text">@CurrentTime</span>
            </div>
        </div>
    </div>
</header>


@code {
    [Parameter] public string LogoText { get; set; } = "Cr";
    [Parameter] public string UserName { get; set; } = "Mike J";
    [Parameter] public string DropdownText { get; set; } = "All";
    [Parameter] public List<string> DropdownOptions { get; set; } = new() { "All", "Active", "Pending", "Completed" };
    [Parameter] public List<NavigationItem> NavigationItems { get; set; } = new();
    [Parameter] public EventCallback<NavigationItem> OnNavigationChanged { get; set; }
    [Parameter] public EventCallback<string> OnDropdownChanged { get; set; }

    private bool IsDropdownOpen { get; set; } = false;
    private string CurrentTime => DateTime.Now.ToString("hh:mm tt");

    protected override void OnInitialized()
    {
        // Výchozí navigační položky
        if (!NavigationItems.Any())
        {
            NavigationItems = new List<NavigationItem>
            {
                new("📷", "Bookings", "/bookings", true),
                new("📋", "Controller", "/controller", false),
                new("🗺️", "Map", "/map", false),
                new("📊", "Reports", "/reports", false)
            };
        }
    }

    private async Task ToggleDropdown()
    {
        IsDropdownOpen = !IsDropdownOpen;
    }

    private async Task OnNavigationClick(NavigationItem item)
    {
        // Deaktivovat všechny položky
        foreach (var navItem in NavigationItems)
        {
            navItem.IsActive = false;
        }
        
        // Aktivovat vybranou položku
        item.IsActive = true;
        
        if (OnNavigationChanged.HasDelegate)
        {
            await OnNavigationChanged.InvokeAsync(item);
        }
        
        StateHasChanged();
    }

    private async Task OnDropdownSelect(string option)
    {
        DropdownText = option;
        IsDropdownOpen = false;
        
        if (OnDropdownChanged.HasDelegate)
        {
            await OnDropdownChanged.InvokeAsync(option);
        }
        
        StateHasChanged();
    }

    public class NavigationItem
    {
        public string Icon { get; set; }
        public string Text { get; set; }
        public string Url { get; set; }
        public bool IsActive { get; set; }

        public NavigationItem() { }

        public NavigationItem(string icon, string text, string url, bool isActive = false)
        {
            Icon = icon;
            Text = text;
            Url = url;
            IsActive = isActive;
        }
    }
}