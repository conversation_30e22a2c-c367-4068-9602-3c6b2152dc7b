@inherits LayoutComponentBase
@implements IDisposable

<div class="layout has-sidebar fixed-sidebar fixed-header">
    <SidebarComponent @ref="_sidebarComponent" />
    
    <div class="layout">
        <HeaderComponentNew />
        
        <main class="content" @onclick="HandleContentClick">
            <div>
                <button @onclick="ToggleSidebar" 
                        id="btn-toggle" 
                        class="sidebar-toggler break-point-sm"
                        aria-label="Toggle sidebar navigation"
                        aria-expanded="true">
                    <i class="ri-menu-line ri-xl" aria-hidden="true"></i>
                </button>
            </div>
            
            <div class="main-content">
                @Body
            </div>
            
            <FooterComponent />
        </main>

        @if (_showOverlay)
        {
            <div class="overlay" @onclick="CloseSidebar" role="presentation"></div>
        }
    </div>
</div>

@code {
    private SidebarComponent? _sidebarComponent;
    private bool _showOverlay;

    protected override void OnInitialized()
    {
        // Initialization logic can be added here if needed
    }

    private async Task ToggleSidebar()
    {
        if (_sidebarComponent is not null)
        {
            await _sidebarComponent.ToggleAsync();
            _showOverlay = await _sidebarComponent.IsToggledAsync();
        }
    }

    private async Task CloseSidebar()
    {
        if (_sidebarComponent is not null && await _sidebarComponent.IsToggledAsync())
        {
            await _sidebarComponent.ToggleAsync();
            _showOverlay = false;
        }
    }

    private async Task HandleContentClick()
    {
        if (_sidebarComponent is not null)
        {
            await _sidebarComponent.CloseAllMenusAsync();
        }
    }

    public void Dispose()
    {
        // Cleanup resources if needed
    }
}
