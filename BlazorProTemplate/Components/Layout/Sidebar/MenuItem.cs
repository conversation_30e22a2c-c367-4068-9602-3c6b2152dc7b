namespace BlazorProTemplate.Components.Layout.Sidebar;

/// <summary>
/// Represents a visual suffix that can be displayed next to a menu item.
/// </summary>
public record Suffix(string Title, string Class);

/// <summary>
/// Represents a single item in the sidebar menu.
/// </summary>
public class MenuItem : IEquatable<MenuItem>
{
    private const int ItemHeight = 50;
    private bool _isOpened;
    private bool _isActive;

    public MenuItem(string? href = null, string? title = null, string? icon = null, Suffix? suffix = null, List<MenuItem>? childMenuItems = null)
    {
        Href = href;
        Title = title;
        Icon = icon;
        Suffix = suffix;
        ChildMenuItems = childMenuItems ?? new List<MenuItem>();
    }

    /// <summary>
    /// Gets or sets the navigation link for this menu item.
    /// </summary>
    public string? Href { get; set; }
    
    /// <summary>
    /// Gets or sets the display text of the menu item.
    /// </summary>
    public string? Title { get; set; }
    
    /// <summary>
    /// Gets or sets the icon class for the menu item.
    /// </summary>
    public string? Icon { get; set; }
    
    /// <summary>
    /// Gets or sets an optional suffix that can be displayed with the menu item.
    /// </summary>
    public Suffix? Suffix { get; set; }
    
    /// <summary>
    /// Gets or sets the child menu items of this menu item.
    /// </summary>
    public List<MenuItem> ChildMenuItems { get; set; }

    public bool IsOpened
    {
        get => _isOpened;
        set
        {
            if (!HasChildMenuItems) return;

            foreach (var item in ChildMenuItems)
            {
                item.IsActive = value;
                if (!value)
                {
                    item.IsOpened = false;
                }
            }

            _isOpened = value;
        }
    }

    public bool IsActive
    {
        get => _isActive;
        set
        {
            if (_isActive != value)
            {
                _isActive = value;
            }
        }
    }

    public bool HasChildMenuItems => ChildMenuItems.Count > 0;

    public int CalcSubMenuHeight()
    {
        if (!IsOpened || !HasChildMenuItems)
            return 0;

        int sumHeight = 0;
        foreach (var childMenuItem in ChildMenuItems)
        {
            sumHeight += childMenuItem.CalcSubMenuHeight();
        }

        return sumHeight + ChildMenuItems.Count * ItemHeight;
    }

    #region IEquatable Implementation
    
    public bool Equals(MenuItem? other)
    {
        if (other is null) return false;
        if (ReferenceEquals(this, other)) return true;
        
        return Href == other.Href && 
               Title == other.Title && 
               Icon == other.Icon &&
               Equals(Suffix, other.Suffix) &&
               ChildMenuItems.SequenceEqual(other.ChildMenuItems);
    }

    public override bool Equals(object? obj)
    {
        if (obj is MenuItem other)
            return Equals(other);
        return false;
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(Href, Title, Icon, Suffix, 
            ChildMenuItems.Aggregate(19, (current, item) => current * 31 + (item?.GetHashCode() ?? 0)));
    }
    
    public static bool operator ==(MenuItem? left, MenuItem? right) => Equals(left, right);
    public static bool operator !=(MenuItem? left, MenuItem? right) => !Equals(left, right);
    
    #endregion
}
