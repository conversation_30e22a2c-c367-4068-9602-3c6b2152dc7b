
@if (MenuItem is not null)
{
    <li class="menu-item @(MenuItem.HasChildMenuItems ? "sub-menu" : "") @(MenuItem.IsOpened ? "open" : "")">
        <NavLink 
            @onclick="HandleClick" 
            href="@(string.IsNullOrEmpty(MenuItem.Href) ? "#" : MenuItem.Href)" 
            class="@(string.IsNullOrEmpty(MenuItem.Href) ? "no-href" : "")"
            aria-expanded="@(MenuItem.HasChildMenuItems ? MenuItem.IsOpened.ToString().ToLowerInvariant() : null)"
            aria-haspopup="@(MenuItem.HasChildMenuItems ? "true" : null)">
            
            @if (!string.IsNullOrEmpty(MenuItem.Icon))
            {
                <span class="menu-icon" aria-hidden="true">
                    <i class="@MenuItem.Icon"></i>
                </span>
            }
            
            <span class="menu-title">@MenuItem?.Title</span>
            
            @if (MenuItem?.Suffix is not null)
            {
                <span class="menu-suffix">
                    <span class="badge @MenuItem.Suffix.Class">@MenuItem.Suffix.Title</span>
                </span>
            }
            
            @if (MenuItem?.HasChildMenuItems == true)
            {
                <i class="ri-arrow-right-s-line menu-arrow" aria-hidden="true"></i>
            }
        </NavLink>
        
        @if (MenuItem?.HasChildMenuItems == true)
        {
            <div class="sub-menu-list" style="height: @(MenuItem.IsOpened ? $"{MenuItem.CalcSubMenuHeight()}px" : "0");">
                <ul role="menu">
                    @foreach (var childMenuItem in MenuItem.ChildMenuItems)
                    {
                        <li role="none">
                            <MenuItemComponent 
                                @key="childMenuItem"
                                MenuItem="childMenuItem" 
                                OnClick="OnClick" />
                        </li>
                    }
                </ul>
            </div>
        }
    </li>
}

@code {

    [Parameter]
    public string Role { get; set; }


    [Parameter]
    public MenuItem? MenuItem { get; set; }

    [Parameter]
    public EventCallback<MenuItem> OnClick { get; set; }

    private async Task HandleClick()
    {
        if (MenuItem is not null)
        {
            await OnClick.InvokeAsync(MenuItem);
        }
    }
}
