@inject NavigationManager _navigationManager
@implements IAsyncDisposable

<aside id="sidebar" 
       class="sidebar break-point-sm has-bg-image @(_isCollapsed ? "collapsed" : "") @(_isToggled ? "toggled" : "")"
       role="navigation" 
       aria-label="Main navigation">
    <button @onclick="ToggleCollapse" 
            id="btn-collapse" 
            class="sidebar-collapser"
            aria-label="@(_isCollapsed ? "Expand" : "Collapse") sidebar"
            aria-expanded="@(!_isCollapsed)">
        <i class="ri-arrow-left-s-line" aria-hidden="true"></i>
    </button>
    <div class="sidebar-layout">
        <header class="sidebar-header">
            <div class="pro-sidebar-logo" role="banner">
                <div role="img" aria-label="Pro Sidebar Logo">P</div>
                <h1 class="h5">Pro Sidebar</h1>
            </div>
        </header>
        
        <div class="sidebar-content">
            <nav class="menu open-current-submenu" role="menu">
                <ul role="none">
                    <li class="menu-header" role="presentation">
                        <span role="heading" aria-level="2">BLAZOR</span>
                    </li>

                    @foreach (var menuItem in _standardItems)
                    {
                        <li role="none">
                            <MenuItemComponent @key="menuItem" 
                                           MenuItem="@menuItem" 
                                           OnClick="HandleMenuItemClick" 
                                           role="menuitem" />
                        </li>
                    }

                    <li class="menu-header" role="presentation">
                        <span role="heading" aria-level="2">GENERAL</span>
                    </li>

                    @foreach (var menuItem in _generalMenuItems)
                    {
                        <li role="none">
                            <MenuItemComponent @key="menuItem" 
                                           MenuItem="@menuItem" 
                                           OnClick="HandleMenuItemClick" 
                                           role="menuitem" />
                        </li>
                    }
                </ul>
            </nav>
        </div>

        <SidebarFooterComponent IsCollapsed="@_isCollapsed" />
    </div>
</aside>

@code {
    private bool _isDisposed;
    private bool _isCollapsed;
    private bool _isToggled;
    private readonly List<MenuItem> _standardItems = new();
    private readonly List<MenuItem> _generalMenuItems = new();
    private readonly SemaphoreSlim _stateLock = new(1, 1);

    /// <summary>
    /// Gets a value indicating whether the sidebar is currently toggled (visible on mobile).
    /// </summary>
    public bool IsToggled => _isToggled;

    protected override void OnInitialized()
    {
        _standardItems.AddRange(SidebarData.GetStandardMenuItems());
        _generalMenuItems.AddRange(SidebarData.GetGeneralMenuItems());
    }

    /// <summary>
    /// Toggles the sidebar's collapsed state.
    /// </summary>
    public async Task ToggleAsync()
    {
        await _stateLock.WaitAsync();
        try
        {
            _isToggled = !_isToggled;
            await InvokeAsync(StateHasChanged);
        }
        finally
        {
            _stateLock.Release();
        }
    }

    /// <summary>
    /// Gets the current toggle state of the sidebar.
    /// </summary>
    public Task<bool> IsToggledAsync() => Task.FromResult(_isToggled);

    /// <summary>
    /// Closes all open menu items.
    /// </summary>
    public async Task CloseAllMenusAsync()
    {
        await _stateLock.WaitAsync();
        try
        {
            foreach (var item in _generalMenuItems)
            {
                item.IsOpened = false;
            }
            await InvokeAsync(StateHasChanged);
        }
        finally
        {
            _stateLock.Release();
        }
    }

    private async Task ToggleCollapse()
    {
        await _stateLock.WaitAsync();
        try
        {
            _isCollapsed = !_isCollapsed;
            
            // Close all menu items when collapsing
            if (_isCollapsed)
            {
                foreach (var item in _generalMenuItems)
                item.IsOpened = false;
            }
            
            await InvokeAsync(StateHasChanged);
        }
        finally
        {
            _stateLock.Release();
        }
    }

    private async Task HandleMenuItemClick(MenuItem menuItem)
    {
        if (menuItem is null) return;
        
        await _stateLock.WaitAsync();
        try
        {
            if (menuItem.IsActive)
            {
                if (menuItem.HasChildMenuItems)
                    menuItem.IsOpened = !menuItem.IsOpened;
                else if (_isCollapsed)
                    await CloseAllMenusAsync();

                return;
            }

            // Close all other menu items
            foreach (var item in _generalMenuItems)
                item.IsOpened = false;
                
            menuItem.IsOpened = true;

            if (!menuItem.HasChildMenuItems && !string.IsNullOrEmpty(menuItem.Href))
                _navigationManager.NavigateTo(menuItem.Href);
                
            await InvokeAsync(StateHasChanged);
        }
        finally
        {
            _stateLock.Release();
        }
    }

    public async ValueTask DisposeAsync()
    {
        if (!_isDisposed)
        {
            _isDisposed = true;
            _stateLock?.Dispose();
            await ValueTask.CompletedTask;
        }
    }

}
