<footer class="sidebar-footer @(IsCollapsed ? "collapsed" : "")" 
        aria-hidden="@IsCollapsed">
    <div class="footer-box">
        <div class="account card" role="region" aria-label="Account Summary">
            <div class="account-cash" aria-label="Current balance">$ 5,637.04</div>
            <div class="account-income">Total Income</div>
            <div class="account-iban" aria-hidden="true">**** **** **** 3060</div>
            <span class="visually-hidden">Account number ending in 3060</span>
        </div>
    </div>
</footer>

@code {
    /// <summary>
    /// Gets or sets a value indicating whether the footer is collapsed.
    /// </summary>
    [Parameter]
    public bool IsCollapsed { get; set; }
}

<style>
    .sidebar-footer {
        transition: opacity 0.3s ease-in-out;
        padding: 1rem;
    }
    
    .sidebar-footer.collapsed {
        opacity: 0;
        height: 0;
        padding: 0;
        margin: 0;
        overflow: hidden;
    }
    
    .footer-box {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .footer-content {
        padding: 0 0.625rem;
    }
    
    .footer-text {
        display: block;
        margin-bottom: 0.625rem;
        font-size: 0.875rem;
    }
    
    .footer-badge {
        margin-bottom: 0.9375rem;
    }
    
    .footer-link a {
        color: var(--primary-color, #4b6cb7);
        text-decoration: none;
        font-weight: 500;
    }
    
    .footer-link a:hover {
        text-decoration: underline;
    }
    
    .visually-hidden {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
    }
</style>
