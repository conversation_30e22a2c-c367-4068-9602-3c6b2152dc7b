namespace BlazorProTemplate.Components.Models;

/// <summary>
/// Represents the status of a table row item.
/// </summary>
public enum TableRowStatus
{
    Active,
    Rejected,
    Waiting
}

/// <summary>
/// Represents a single row of data in the data table.
/// </summary>
public class TableRowData
{
    /// <summary>
    /// Gets or sets the unique identifier for the row.
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the time or identifier displayed in the first column.
    /// </summary>
    public string TimeId { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the name of the person.
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the origin location.
    /// </summary>
    public string From { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the destination location.
    /// </summary>
    public string To { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the V column value.
    /// </summary>
    public string VValue { get; set; } = "N";

    /// <summary>
    /// Gets or sets whether the B column is active (shows euro icon).
    /// </summary>
    public bool BActive { get; set; } = true;

    /// <summary>
    /// Gets or sets whether the C column is active (shows euro icon).
    /// </summary>
    public bool CActive { get; set; } = true;

    /// <summary>
    /// Gets or sets whether the A column is active (shows fire icon).
    /// </summary>
    public bool AActive { get; set; } = true;

    /// <summary>
    /// Gets or sets whether the W column is active (shows plane icon).
    /// </summary>
    public bool WActive { get; set; } = true;

    /// <summary>
    /// Gets or sets the status of the row.
    /// </summary>
    public TableRowStatus Status { get; set; } = TableRowStatus.Active;

    /// <summary>
    /// Gets or sets whether the row is selected.
    /// </summary>
    public bool IsSelected { get; set; } = false;

    /// <summary>
    /// Gets the CSS class for the status.
    /// </summary>
    public string StatusClass => Status switch
    {
        TableRowStatus.Active => "status-active",
        TableRowStatus.Rejected => "status-rejected",
        TableRowStatus.Waiting => "status-waiting",
        _ => "status-active"
    };

    /// <summary>
    /// Gets the display text for the status.
    /// </summary>
    public string StatusText => Status switch
    {
        TableRowStatus.Active => "Active",
        TableRowStatus.Rejected => "Rejected",
        TableRowStatus.Waiting => "Waiting",
        _ => "Active"
    };
}
