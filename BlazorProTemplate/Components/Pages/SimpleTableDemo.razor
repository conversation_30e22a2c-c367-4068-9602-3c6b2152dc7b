@page "/simple-table"
@using BlazorProTemplate.Components.Models

<PageTitle>Simple Table</PageTitle>

<div class="main-content">
    <h1>Simple Data Table</h1>
    
    <DataTable Data="@SampleData" />
</div>

@code {
    private List<TableRowData> SampleData = new()
    {
        new TableRowData
        {
            Id = "1",
            TimeId = "17.00",
            Name = "John D",
            From = "Sudbury Station",
            To = "Center Plaza",
            VValue = "N",
            BActive = true,
            CActive = true,
            AActive = true,
            WActive = false,
            Status = TableRowStatus.Active
        },
        new TableRowData
        {
            Id = "2",
            TimeId = "17.00",
            Name = "Rufi",
            From = "One Beacon",
            To = "Los Angeles",
            VValue = "N",
            BActive = true,
            CActive = true,
            AActive = false,
            WActive = true,
            Status = TableRowStatus.Rejected
        },
        new TableRowData
        {
            Id = "3",
            TimeId = "17.00",
            Name = "Alfred",
            From = "5 Main High",
            To = "Center Plaza",
            VValue = "N",
            BActive = true,
            CActive = true,
            AActive = true,
            WActive = false,
            Status = TableRowStatus.Waiting
        }
    };
}
