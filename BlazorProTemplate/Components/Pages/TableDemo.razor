@page "/table-demo"
@using BlazorProTemplate.Components.Models

<PageTitle>Data Table Demo</PageTitle>

<div class="main-content">
    <div class="page-header">
        <h1>Data Table Demo</h1>
        <p>Demonstration of the custom data table component based on the provided design.</p>
    </div>

    <div class="table-section">
        <div class="table-controls">
            <div class="selected-info">
                @if (SelectedRows.Any())
                {
                    <span class="selected-count">@SelectedRows.Count selected</span>
                    <button class="btn btn-primary" @onclick="ClearSelection">Clear Selection</button>
                }
            </div>
        </div>

        <DataTable Data="@TableData" 
                   OnSelectionChanged="@HandleSelectionChanged" 
                   OnRowClick="@HandleRowClick" />
    </div>

    @if (SelectedRows.Any())
    {
        <div class="selection-details">
            <h3>Selected Items:</h3>
            <ul>
                @foreach (var row in SelectedRows)
                {
                    <li>@row.Name - @row.From to @row.To (@row.StatusText)</li>
                }
            </ul>
        </div>
    }
</div>

<style>
    .page-header {
        margin-bottom: 2rem;
    }

    .page-header h1 {
        color: var(--color-text-primary);
        margin-bottom: 0.5rem;
    }

    .page-header p {
        color: var(--color-text-secondary);
        margin-bottom: 0;
    }

    .table-section {
        margin-bottom: 2rem;
    }

    .table-controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
        padding: 1rem;
        background-color: var(--color-bg-secondary);
        border-radius: var(--border-radius);
    }

    .selected-info {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .selected-count {
        color: var(--color-text-secondary);
        font-weight: 500;
    }

    .selection-details {
        padding: 1rem;
        background-color: var(--color-bg-secondary);
        border-radius: var(--border-radius);
        border-left: 4px solid var(--color-primary);
    }

    .selection-details h3 {
        color: var(--color-text-primary);
        margin-bottom: 1rem;
    }

    .selection-details ul {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .selection-details li {
        padding: 0.5rem 0;
        color: var(--color-text-secondary);
        border-bottom: 1px solid var(--color-border);
    }

    .selection-details li:last-child {
        border-bottom: none;
    }
</style>

@code {
    private List<TableRowData> TableData = new();
    private List<TableRowData> SelectedRows = new();

    protected override void OnInitialized()
    {
        // Generate sample data based on the image
        TableData = new List<TableRowData>
        {
            new TableRowData
            {
                Id = "1",
                TimeId = "17.00",
                Name = "John D",
                From = "Sudbury Station",
                To = "Center Plaza",
                VValue = "N",
                BActive = true,
                CActive = true,
                AActive = true,
                WActive = false,
                Status = TableRowStatus.Active
            },
            new TableRowData
            {
                Id = "2",
                TimeId = "17.00",
                Name = "Rufi",
                From = "One Beacon",
                To = "Los Angeles",
                VValue = "N",
                BActive = true,
                CActive = true,
                AActive = false,
                WActive = true,
                Status = TableRowStatus.Rejected
            },
            new TableRowData
            {
                Id = "3",
                TimeId = "17.00",
                Name = "Alfred",
                From = "5 Main High",
                To = "Center Plaza",
                VValue = "N",
                BActive = true,
                CActive = true,
                AActive = true,
                WActive = false,
                Status = TableRowStatus.Waiting
            },
            new TableRowData
            {
                Id = "4",
                TimeId = "17.00",
                Name = "Mike J.",
                From = "Brooklyn 99",
                To = "Park, NY",
                VValue = "N",
                BActive = true,
                CActive = true,
                AActive = false,
                WActive = true,
                Status = TableRowStatus.Waiting
            },
            new TableRowData
            {
                Id = "5",
                TimeId = "17.00",
                Name = "Hermann B.",
                From = "Jamburg Station",
                To = "Center Park",
                VValue = "N",
                BActive = true,
                CActive = true,
                AActive = false,
                WActive = true,
                Status = TableRowStatus.Active
            }
        };
    }

    private void HandleSelectionChanged(List<TableRowData> selectedRows)
    {
        SelectedRows = selectedRows;
        StateHasChanged();
    }

    private void HandleRowClick(TableRowData row)
    {
        // Handle row click - could navigate to detail page, show modal, etc.
        Console.WriteLine($"Row clicked: {row.Name}");
    }

    private void ClearSelection()
    {
        foreach (var row in TableData)
        {
            row.IsSelected = false;
        }
        SelectedRows.Clear();
        StateHasChanged();
    }
}
