@using BlazorProTemplate.Components.Models

<div class="data-table-container">
    <table class="table data-table">
        <thead>
            <tr>
                <th class="checkbox-column">
                    <input type="checkbox" 
                           @onchange="ToggleSelectAll" 
                           checked="@IsAllSelected"
                           aria-label="Select all rows" />
                </th>
                <th class="time-column">T</th>
                <th class="name-column">Name</th>
                <th class="from-column">From</th>
                <th class="to-column">To</th>
                <th class="icon-column">V</th>
                <th class="icon-column">B</th>
                <th class="icon-column">C</th>
                <th class="icon-column">A</th>
                <th class="icon-column">W</th>
                <th class="status-column">Status</th>
            </tr>
        </thead>
        <tbody>
            @if (Data != null && Data.Any())
            {
                @foreach (var row in Data)
                {
                    <tr class="@(row.IsSelected ? "selected" : "")">
                        <td class="checkbox-column">
                            <input type="checkbox" 
                                   @onchange="(e) => ToggleRowSelection(row, e)" 
                                   checked="@row.IsSelected"
                                   aria-label="Select row" />
                        </td>
                        <td class="time-column">
                            <span class="time-badge">@row.TimeId</span>
                        </td>
                        <td class="name-column">@row.Name</td>
                        <td class="from-column">@row.From</td>
                        <td class="to-column">@row.To</td>
                        <td class="icon-column">@row.VValue</td>
                        <td class="icon-column">
                            @if (row.BActive)
                            {
                                <span class="icon-euro">€</span>
                            }
                        </td>
                        <td class="icon-column">
                            @if (row.CActive)
                            {
                                <span class="icon-euro">€</span>
                            }
                        </td>
                        <td class="icon-column">
                            @if (row.AActive)
                            {
                                <span class="icon-fire">🔥</span>
                            }
                        </td>
                        <td class="icon-column">
                            @if (row.WActive)
                            {
                                <span class="icon-plane">✈️</span>
                            }
                        </td>
                        <td class="status-column">
                            <span class="status @row.StatusClass">
                                @if (row.Status == TableRowStatus.Active)
                                {
                                    <span class="status-icon">✓</span>
                                }
                                else if (row.Status == TableRowStatus.Rejected)
                                {
                                    <span class="status-icon">✗</span>
                                }
                                else if (row.Status == TableRowStatus.Waiting)
                                {
                                    <span class="status-icon">⏳</span>
                                }
                                @row.StatusText
                            </span>
                        </td>
                    </tr>
                }
            }
            else
            {
                <tr>
                    <td colspan="11" class="no-data">
                        <p>No data available</p>
                    </td>
                </tr>
            }
        </tbody>
    </table>
</div>

@code {
    /// <summary>
    /// Gets or sets the data to display in the table.
    /// </summary>
    [Parameter] public List<TableRowData>? Data { get; set; }

    /// <summary>
    /// Gets or sets the callback for when row selection changes.
    /// </summary>
    [Parameter] public EventCallback<List<TableRowData>> OnSelectionChanged { get; set; }

    /// <summary>
    /// Gets or sets the callback for when a row is clicked.
    /// </summary>
    [Parameter] public EventCallback<TableRowData> OnRowClick { get; set; }

    /// <summary>
    /// Gets whether all rows are selected.
    /// </summary>
    private bool IsAllSelected => Data?.Any() == true && Data.All(x => x.IsSelected);

    /// <summary>
    /// Toggles selection of all rows.
    /// </summary>
    private async Task ToggleSelectAll(ChangeEventArgs e)
    {
        if (Data == null) return;

        bool selectAll = (bool)(e.Value ?? false);
        
        foreach (var row in Data)
        {
            row.IsSelected = selectAll;
        }

        await NotifySelectionChanged();
        StateHasChanged();
    }

    /// <summary>
    /// Toggles selection of a specific row.
    /// </summary>
    private async Task ToggleRowSelection(TableRowData row, ChangeEventArgs e)
    {
        row.IsSelected = (bool)(e.Value ?? false);
        await NotifySelectionChanged();
        StateHasChanged();
    }

    /// <summary>
    /// Notifies parent component of selection changes.
    /// </summary>
    private async Task NotifySelectionChanged()
    {
        if (OnSelectionChanged.HasDelegate && Data != null)
        {
            var selectedRows = Data.Where(x => x.IsSelected).ToList();
            await OnSelectionChanged.InvokeAsync(selectedRows);
        }
    }

    /// <summary>
    /// Handles row click events.
    /// </summary>
    private async Task HandleRowClick(TableRowData row)
    {
        if (OnRowClick.HasDelegate)
        {
            await OnRowClick.InvokeAsync(row);
        }
    }
}
