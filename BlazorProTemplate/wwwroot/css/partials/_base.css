/* ============================================
   Base & Reset
   ============================================ */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    height: 100%;
}

body {
    min-height: 100%;
    font-family: var(--font-family-base);
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
    color: var(--color-primary);
    background-color: var(--color-background);
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    margin-bottom: var(--spacing-md);
    font-weight: 500;
    line-height: 1.2;
    color: var(--color-light);
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
    margin-top: 0;
    margin-bottom: var(--spacing-md);
}

/* Links */
a {
    color: var(--color-accent);
    text-decoration: none;
    background-color: transparent;
    transition: color var(--transition-duration) var(--transition-timing);
}

a:hover {
    color: var(--color-accent-light);
    text-decoration: none;
}

/* Lists */
ul, ol {
    padding-left: var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

/* Images */
img {
    vertical-align: middle;
    border-style: none;
    max-width: 100%;
    height: auto;
}

/* Buttons */
button {
    border-radius: 0;
}

button:focus {
    outline: 1px dotted;
    outline: 5px auto -webkit-focus-ring-color;
}

/* Forms */
input,
button,
select,
optgroup,
textarea {
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
}

/* Tables */
table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: var(--spacing-md);
    color: var(--color-primary);
    background-color: transparent;
}

th {
    text-align: inherit;
    text-align: -webkit-match-parent;
}

/* Horizontal rules */
hr {
    box-sizing: content-box;
    height: 0;
    overflow: visible;
    margin: var(--spacing-lg) 0;
    border: 0;
    border-top: 1px solid var(--color-border);
}

/* Code */
code {
    font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    font-size: 0.875em;
    color: var(--code-color);
    background-color: var(--code-bg);
    padding: 0.2em 0.4em;
    border-radius: var(--border-radius-sm);
    word-wrap: break-word;
}

/* Preformatted text */
pre {
    display: block;
    font-size: 0.875em;
    color: var(--pre-color);
    background-color: var(--pre-bg);
    margin-top: 0;
    margin-bottom: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    overflow: auto;
}

pre code {
    font-size: inherit;
    color: inherit;
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    word-break: normal;
}

/* Keyboard input */
kbd {
    padding: 0.2rem 0.4rem;
    font-size: 0.875em;
    color: var(--kbd-color);
    background-color: var(--kbd-bg);
    border-radius: var(--border-radius-sm);
    box-shadow: inset 0 -0.1rem 0 rgba(0, 0, 0, 0.25);
}

/* Figures */
figure {
    margin: 0 0 var(--spacing-md);
}

/* Embedded content */
img,
svg {
    vertical-align: middle;
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
    position: static;
    width: auto;
    height: auto;
    overflow: visible;
    clip: auto;
    white-space: normal;
}
