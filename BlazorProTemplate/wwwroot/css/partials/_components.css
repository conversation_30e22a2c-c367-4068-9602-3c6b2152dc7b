/* ============================================
   Components
   ============================================ */

/* Sidebar Components */
.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--sidebar-divider);
    text-align: center;
}

.sidebar-logo {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 0 auto;
    transition: var(--sidebar-transition);
}

.sidebar.collapsed .sidebar-logo {
    max-width: 70%;
}

/* Menu Component */
.menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu-item {
    position: relative;
}

.menu-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--sidebar-color);
    transition: var(--sidebar-transition);
    position: relative;
    text-decoration: none;
    white-space: nowrap;
}

.menu-link:hover,
.menu-link:focus {
    color: var(--sidebar-hover-color);
    background-color: var(--color-hover);
}

.menu-link.active {
    color: var(--sidebar-active-color);
    font-weight: 500;
}

.menu-icon {
    margin-right: var(--spacing-md);
    width: 1.5rem;
    text-align: center;
    font-size: 1.1rem;
    transition: var(--sidebar-transition);
}

.menu-title {
    transition: opacity var(--transition-duration) var(--transition-timing);
    flex-grow: 1;
}

.menu-badge {
    padding: var(--spacing-xxs) var(--spacing-xs);
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    border-radius: var(--border-radius-pill);
    background-color: var(--color-accent);
    color: white;
}

/* Submenu */
.sub-menu {
    overflow: hidden;
}

.sub-menu .sub-menu-list {
    max-height: 0;
    overflow: hidden;
    transition: max-height var(--transition-duration) var(--transition-timing);
    background-color: var(--sidebar-submenu-bg);
    padding-left: 0;
}

.sub-menu.open > .sub-menu-list {
    max-height: 1000px; /* Adjust based on your needs */
}

.sub-menu .menu-link {
    padding-left: calc(var(--spacing-lg) * 2);
}

.sub-menu .sub-menu .menu-link {
    padding-left: calc(var(--spacing-lg) * 3);
}

/* Card Component */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: var(--card-bg);
    background-clip: border-box;
    border: 1px solid var(--card-border-color);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
}

.card-body {
    flex: 1 1 auto;
    padding: var(--spacing-lg);
}

.card-header {
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: 0;
    background-color: var(--card-cap-bg);
    border-bottom: 1px solid var(--card-border-color);
}

.card-title {
    margin-bottom: var(--spacing-md);
}

/* Button Component */
.btn {
    display: inline-block;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    line-height: 1.5;
    border-radius: var(--border-radius);
    transition: var(--transition);
    cursor: pointer;
}

.btn-primary {
    color: #fff;
    background-color: var(--color-accent);
    border-color: var(--color-accent);
}

.btn-primary:hover {
    background-color: var(--color-accent-light);
    border-color: var(--color-accent-light);
}

/* Alert Component */
.alert {
    position: relative;
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    border: 1px solid transparent;
    border-radius: var(--border-radius);
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* Badge Component */
.badge {
    display: inline-block;
    padding: var(--spacing-xxs) var(--spacing-xs);
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--border-radius-pill);
}

.badge-primary {
    color: #fff;
    background-color: var(--color-accent);
}

/* Form Components */
.form-group {
    margin-bottom: var(--spacing-md);
}

.form-control {
    display: block;
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--color-primary);
    background-color: var(--color-dark);
    background-clip: padding-box;
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    transition: border-color var(--transition-duration) var(--transition-timing);
}

.form-control:focus {
    color: var(--color-light);
    background-color: var(--color-darker);
    border-color: var(--color-accent);
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(66, 85, 212, 0.25);
}

/* Table Component */
.table {
    width: 100%;
    margin-bottom: var(--spacing-md);
    color: var(--color-primary);
    background-color: transparent;
}

.table th,
.table td {
    padding: var(--spacing-sm) var(--spacing-md);
    vertical-align: top;
    border-top: 1px solid var(--color-border);
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid var(--color-border);
    font-weight: 600;
}

.table tbody + tbody {
    border-top: 2px solid var(--color-border);
}

/* Modal Component */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    z-index: var(--zindex-modal);
    display: none;
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    outline: 0;
}

.modal.show {
    display: block;
}

.modal-dialog {
    position: relative;
    width: auto;
    margin: var(--spacing-lg) auto;
    max-width: 500px;
    pointer-events: none;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: var(--color-dark);
    background-clip: padding-box;
    border: 1px solid var(--color-border);
    border-radius: var(--border-radius);
    outline: 0;
}

.modal-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    border-bottom: 1px solid var(--color-border);
    border-top-left-radius: calc(var(--border-radius) - 1px);
    border-top-right-radius: calc(var(--border-radius) - 1px);
}

.modal-title {
    margin-bottom: 0;
    line-height: 1.5;
}

.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: var(--spacing-lg);
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--color-border);
    border-bottom-right-radius: calc(var(--border-radius) - 1px);
    border-bottom-left-radius: calc(var(--border-radius) - 1px);
}

.modal-footer > * {
    margin: 0 0 0 var(--spacing-sm);
}

/* Tooltip */
.tooltip {
    position: absolute;
    z-index: var(--zindex-tooltip);
    display: block;
    margin: 0;
    font-style: normal;
    font-weight: 400;
    line-height: 1.5;
    text-align: left;
    text-align: start;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    letter-spacing: normal;
    word-break: normal;
    white-space: normal;
    line-break: auto;
    font-size: 0.875rem;
    word-wrap: break-word;
    opacity: 0;
}

.tooltip.show {
    opacity: 0.9;
}

.tooltip-arrow {
    position: absolute;
    display: block;
    width: 0.8rem;
    height: 0.4rem;
}

.tooltip-arrow::before {
    position: absolute;
    content: "";
    border-color: transparent;
    border-style: solid;
}

.bs-tooltip-top .tooltip-arrow,
.bs-tooltip-auto[x-placement^="top"] .tooltip-arrow {
    bottom: 0;
}

.bs-tooltip-top .tooltip-arrow::before,
.bs-tooltip-auto[x-placement^="top"] .tooltip-arrow::before {
    top: 0;
    border-width: 0.4rem 0.4rem 0;
    border-top-color: #000;
}

.tooltip-inner {
    max-width: 200px;
    padding: 0.25rem 0.5rem;
    color: #fff;
    text-align: center;
    background-color: #000;
    border-radius: var(--border-radius);
}