/* ============================================
   Layout Components
   ============================================ */

/* Main Layout Wrapper */
.layout {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    position: relative;
    width: 100%;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    right: 0;
    left: var(--sidebar-width);
    height: var(--header-height);
    background-color: var(--header-bg);
    color: var(--header-color);
    z-index: var(--zindex-fixed);
    transition: var(--sidebar-transition);
    display: flex;
    align-items: center;
    padding: 0 var(--spacing-lg);
    box-shadow: var(--box-shadow-sm);
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    width: var(--sidebar-width);
    background-color: var(--sidebar-bg);
    color: var(--sidebar-color);
    z-index: var(--zindex-fixed);
    transition: var(--sidebar-transition);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    overflow-x: hidden;
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar.collapsed ~ .header,
.sidebar.collapsed ~ .main-content {
    left: var(--sidebar-collapsed-width);
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    margin-top: var(--header-height);
    padding: var(--spacing-lg);
    min-height: calc(100vh - var(--header-height));
    transition: var(--sidebar-transition);
}

/* Footer */
.footer {
    padding: var(--spacing-lg);
    background-color: var(--color-dark);
    color: var(--color-primary);
    margin-top: auto;
}

/* Container */
.container {
    width: 100%;
    padding-right: var(--spacing-lg);
    padding-left: var(--spacing-lg);
    margin-right: auto;
    margin-left: auto;
}

@media (min-width: 576px) {
    .container {
        max-width: 540px;
    }
}

@media (min-width: 768px) {
    .container {
        max-width: 720px;
    }
}

@media (min-width: 992px) {
    .container {
        max-width: 960px;
    }
}

@media (min-width: 1200px) {
    .container {
        max-width: 1140px;
    }
}

/* Grid */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: calc(-0.5 * var(--spacing-md));
    margin-left: calc(-0.5 * var(--spacing-md));
}

.col {
    flex: 1 0 0;
    padding-right: calc(0.5 * var(--spacing-md));
    padding-left: calc(0.5 * var(--spacing-md));
}

/* Responsive Utilities */
@media (max-width: 992px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .header,
    .main-content {
        left: 0 !important;
    }
    
    .sidebar.collapsed {
        transform: translateX(-100%);
    }
    
    .sidebar.collapsed.show {
        transform: translateX(0);
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .header {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
        margin-top: 0 !important;
        padding: 0 !important;
    }
}
