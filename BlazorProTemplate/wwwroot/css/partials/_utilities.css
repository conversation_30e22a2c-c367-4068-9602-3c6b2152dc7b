/* ============================================
   Utility Classes
   ============================================ */

/* Display Utilities */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }
.d-table { display: table !important; }
.d-table-row { display: table-row !important; }
.d-table-cell { display: table-cell !important; }

/* Flex Utilities */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.align-content-start { align-content: flex-start !important; }
.align-content-end { align-content: flex-end !important; }
.align-content-center { align-content: center !important; }
.align-content-between { align-content: space-between !important; }
.align-content-around { align-content: space-around !important; }
.align-content-stretch { align-content: stretch !important; }

.align-self-auto { align-self: auto !important; }
.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

.flex-fill { flex: 1 1 auto !important; }
.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

/* Spacing Utilities */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--spacing-xxs) !important; }
.m-2 { margin: var(--spacing-xs) !important; }
.m-3 { margin: var(--spacing-sm) !important; }
.m-4 { margin: var(--spacing-md) !important; }
.m-5 { margin: var(--spacing-lg) !important; }
.m-6 { margin: var(--spacing-xl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--spacing-xxs) !important; }
.mt-2 { margin-top: var(--spacing-xs) !important; }
.mt-3 { margin-top: var(--spacing-sm) !important; }
.mt-4 { margin-top: var(--spacing-md) !important; }
.mt-5 { margin-top: var(--spacing-lg) !important; }
.mt-6 { margin-top: var(--spacing-xl) !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: var(--spacing-xxs) !important; }
.mr-2 { margin-right: var(--spacing-xs) !important; }
.mr-3 { margin-right: var(--spacing-sm) !important; }
.mr-4 { margin-right: var(--spacing-md) !important; }
.mr-5 { margin-right: var(--spacing-lg) !important; }
.mr-6 { margin-right: var(--spacing-xl) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--spacing-xxs) !important; }
.mb-2 { margin-bottom: var(--spacing-xs) !important; }
.mb-3 { margin-bottom: var(--spacing-sm) !important; }
.mb-4 { margin-bottom: var(--spacing-md) !important; }
.mb-5 { margin-bottom: var(--spacing-lg) !important; }
.mb-6 { margin-bottom: var(--spacing-xl) !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: var(--spacing-xxs) !important; }
.ml-2 { margin-left: var(--spacing-xs) !important; }
.ml-3 { margin-left: var(--spacing-sm) !important; }
.ml-4 { margin-left: var(--spacing-md) !important; }
.ml-5 { margin-left: var(--spacing-lg) !important; }
.ml-6 { margin-left: var(--spacing-xl) !important; }

.mx-0 { margin-right: 0 !important; margin-left: 0 !important; }
.mx-1 { margin-right: var(--spacing-xxs) !important; margin-left: var(--spacing-xxs) !important; }
.mx-2 { margin-right: var(--spacing-xs) !important; margin-left: var(--spacing-xs) !important; }
.mx-3 { margin-right: var(--spacing-sm) !important; margin-left: var(--spacing-sm) !important; }
.mx-4 { margin-right: var(--spacing-md) !important; margin-left: var(--spacing-md) !important; }
.mx-5 { margin-right: var(--spacing-lg) !important; margin-left: var(--spacing-lg) !important; }
.mx-6 { margin-right: var(--spacing-xl) !important; margin-left: var(--spacing-xl) !important; }

.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-1 { margin-top: var(--spacing-xxs) !important; margin-bottom: var(--spacing-xxs) !important; }
.my-2 { margin-top: var(--spacing-xs) !important; margin-bottom: var(--spacing-xs) !important; }
.my-3 { margin-top: var(--spacing-sm) !important; margin-bottom: var(--spacing-sm) !important; }
.my-4 { margin-top: var(--spacing-md) !important; margin-bottom: var(--spacing-md) !important; }
.my-5 { margin-top: var(--spacing-lg) !important; margin-bottom: var(--spacing-lg) !important; }
.my-6 { margin-top: var(--spacing-xl) !important; margin-bottom: var(--spacing-xl) !important; }

/* Padding Utilities */
.p-0 { padding: 0 !important; }
.p-1 { padding: var(--spacing-xxs) !important; }
.p-2 { padding: var(--spacing-xs) !important; }
.p-3 { padding: var(--spacing-sm) !important; }
.p-4 { padding: var(--spacing-md) !important; }
.p-5 { padding: var(--spacing-lg) !important; }
.p-6 { padding: var(--spacing-xl) !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: var(--spacing-xxs) !important; }
.pt-2 { padding-top: var(--spacing-xs) !important; }
.pt-3 { padding-top: var(--spacing-sm) !important; }
.pt-4 { padding-top: var(--spacing-md) !important; }
.pt-5 { padding-top: var(--spacing-lg) !important; }
.pt-6 { padding-top: var(--spacing-xl) !important; }

.pr-0 { padding-right: 0 !important; }
.pr-1 { padding-right: var(--spacing-xxs) !important; }
.pr-2 { padding-right: var(--spacing-xs) !important; }
.pr-3 { padding-right: var(--spacing-sm) !important; }
.pr-4 { padding-right: var(--spacing-md) !important; }
.pr-5 { padding-right: var(--spacing-lg) !important; }
.pr-6 { padding-right: var(--spacing-xl) !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: var(--spacing-xxs) !important; }
.pb-2 { padding-bottom: var(--spacing-xs) !important; }
.pb-3 { padding-bottom: var(--spacing-sm) !important; }
.pb-4 { padding-bottom: var(--spacing-md) !important; }
.pb-5 { padding-bottom: var(--spacing-lg) !important; }
.pb-6 { padding-bottom: var(--spacing-xl) !important; }

.pl-0 { padding-left: 0 !important; }
.pl-1 { padding-left: var(--spacing-xxs) !important; }
.pl-2 { padding-left: var(--spacing-xs) !important; }
.pl-3 { padding-left: var(--spacing-sm) !important; }
.pl-4 { padding-left: var(--spacing-md) !important; }
.pl-5 { padding-left: var(--spacing-lg) !important; }
.pl-6 { padding-left: var(--spacing-xl) !important; }

.px-0 { padding-right: 0 !important; padding-left: 0 !important; }
.px-1 { padding-right: var(--spacing-xxs) !important; padding-left: var(--spacing-xxs) !important; }
.px-2 { padding-right: var(--spacing-xs) !important; padding-left: var(--spacing-xs) !important; }
.px-3 { padding-right: var(--spacing-sm) !important; padding-left: var(--spacing-sm) !important; }
.px-4 { padding-right: var(--spacing-md) !important; padding-left: var(--spacing-md) !important; }
.px-5 { padding-right: var(--spacing-lg) !important; padding-left: var(--spacing-lg) !important; }
.px-6 { padding-right: var(--spacing-xl) !important; padding-left: var(--spacing-xl) !important; }

.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-1 { padding-top: var(--spacing-xxs) !important; padding-bottom: var(--spacing-xxs) !important; }
.py-2 { padding-top: var(--spacing-xs) !important; padding-bottom: var(--spacing-xs) !important; }
.py-3 { padding-top: var(--spacing-sm) !important; padding-bottom: var(--spacing-sm) !important; }
.py-4 { padding-top: var(--spacing-md) !important; padding-bottom: var(--spacing-md) !important; }
.py-5 { padding-top: var(--spacing-lg) !important; padding-bottom: var(--spacing-lg) !important; }
.py-6 { padding-top: var(--spacing-xl) !important; padding-bottom: var(--spacing-xl) !important; }

/* Text Utilities */
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.text-center { text-align: center !important; }
.text-justify { text-align: justify !important; }

.text-lowercase { text-transform: lowercase !important; }
.text-uppercase { text-transform: uppercase !important; }
.text-capitalize { text-transform: capitalize !important; }

.font-weight-light { font-weight: 300 !important; }
.font-weight-normal { font-weight: 400 !important; }
.font-weight-medium { font-weight: 500 !important; }
.font-weight-semibold { font-weight: 600 !important; }
.font-weight-bold { font-weight: 700 !important; }
.font-italic { font-style: italic !important; }

.text-nowrap { white-space: nowrap !important; }
.text-truncate { 
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.text-break {
    word-wrap: break-word !important;
    word-break: break-word !important;
}

/* Color Utilities */
.text-primary { color: var(--color-primary) !important; }
.text-secondary { color: var(--color-secondary) !important; }
.text-success { color: var(--color-success) !important; }
.text-danger { color: var(--color-danger) !important; }
.text-warning { color: var(--color-warning) !important; }
.text-info { color: var(--color-info) !important; }
.text-light { color: var(--color-light) !important; }
.text-dark { color: var(--color-dark) !important; }
.text-body { color: var(--color-primary) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-white { color: var(--text-white) !important; }
.text-black-50 { color: var(--text-black-50) !important; }
.text-white-50 { color: var(--text-white-50) !important; }

.bg-primary { background-color: var(--color-accent) !important; }
.bg-secondary { background-color: var(--color-secondary) !important; }
.bg-success { background-color: var(--color-success) !important; }
.bg-danger { background-color: var(--color-danger) !important; }
.bg-warning { background-color: var(--color-warning) !important; }
.bg-info { background-color: var(--color-info) !important; }
.bg-light { background-color: var(--color-light) !important; }
.bg-dark { background-color: var(--color-dark) !important; }
.bg-white { background-color: var(--bg-white) !important; }
.bg-transparent { background-color: var(--bg-transparent) !important; }

/* Border Utilities */
.border { border: 1px solid var(--color-border) !important; }
.border-top { border-top: 1px solid var(--color-border) !important; }
.border-right { border-right: 1px solid var(--color-border) !important; }
.border-bottom { border-bottom: 1px solid var(--color-border) !important; }
.border-left { border-left: 1px solid var(--color-border) !important; }

.border-0 { border: 0 !important; }
.border-top-0 { border-top: 0 !important; }
.border-right-0 { border-right: 0 !important; }
.border-bottom-0 { border-bottom: 0 !important; }
.border-left-0 { border-left: 0 !important; }

.rounded { border-radius: var(--border-radius) !important; }
.rounded-top { border-top-left-radius: var(--border-radius) !important; border-top-right-radius: var(--border-radius) !important; }
.rounded-right { border-top-right-radius: var(--border-radius) !important; border-bottom-right-radius: var(--border-radius) !important; }
.rounded-bottom { border-bottom-right-radius: var(--border-radius) !important; border-bottom-left-radius: var(--border-radius) !important; }
.rounded-left { border-top-left-radius: var(--border-radius) !important; border-bottom-left-radius: var(--border-radius) !important; }
.rounded-circle { border-radius: 50% !important; }
.rounded-pill { border-radius: var(--border-radius-pill) !important; }
.rounded-0 { border-radius: 0 !important; }

/* Sizing Utilities */
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

.mw-100 { max-width: 100% !important; }
.mh-100 { max-height: 100% !important; }

.min-vw-100 { min-width: 100vw !important; }
.min-vh-100 { min-height: 100vh !important; }

.vw-100 { width: 100vw !important; }
.vh-100 { height: 100vh !important; }

/* Position Utilities */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* Shadow Utilities */
.shadow-sm { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important; }
.shadow { box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important; }
.shadow-lg { box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important; }
.shadow-none { box-shadow: none !important; }

/* Opacity Utilities */
.opacity-0 { opacity: 0 !important; }
.opacity-25 { opacity: 0.25 !important; }
.opacity-50 { opacity: 0.5 !important; }
.opacity-75 { opacity: 0.75 !important; }
.opacity-100 { opacity: 1 !important; }

/* Overflow Utilities */
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

/* Float Utilities */
.float-left { float: left !important; }
.float-right { float: right !important; }
.float-none { float: none !important; }

/* Clearfix */
.clearfix::after {
    display: block;
    clear: both;
    content: "";
}

/* Visibility Utilities */
.visible { visibility: visible !important; }
.invisible { visibility: hidden !important; }

/* Cursor Utilities */
.cursor-pointer { cursor: pointer !important; }
.cursor-default { cursor: default !important; }
.cursor-move { cursor: move !important; }
.cursor-text { cursor: text !important; }
.cursor-not-allowed { cursor: not-allowed !important; }
.cursor-help { cursor: help !important; }
.cursor-wait { cursor: wait !important; }

/* User Select */
.user-select-all { user-select: all !important; }
.user-select-auto { user-select: auto !important; }
.user-select-none { user-select: none !important; }

/* Pointer Events */
.pointer-events-none { pointer-events: none !important; }
.pointer-events-auto { pointer-events: auto !important; }

/* Print Utilities */
@media print {
    .d-print-none { display: none !important; }
    .d-print-inline { display: inline !important; }
    .d-print-inline-block { display: inline-block !important; }
    .d-print-block { display: block !important; }
    .d-print-table { display: table !important; }
    .d-print-table-row { display: table-row !important; }
    .d-print-table-cell { display: table-cell !important; }
    .d-print-flex { display: flex !important; }
    .d-print-inline-flex { display: inline-flex !important; }
}
