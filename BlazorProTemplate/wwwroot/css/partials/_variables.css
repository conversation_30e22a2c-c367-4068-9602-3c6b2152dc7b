/* ============================================
   CSS Variables & Theming
   ============================================ */
:root {
    /* Animation */
    --transition-duration: 0.3s;
    --transition-timing: ease-in-out;
    --transition: all var(--transition-duration) var(--transition-timing);
    
    /* Typography */
    --font-family-base: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-size-base: 0.9rem;
    --line-height-base: 1.5;
    
    /* ========================================
       ZÁKLADNÍ MODRÉ TÓNY (3 odstíny)
       ======================================== */

    /* Nejtmavší modrá - hlavní pozadí aplikace */
    --color-bg-primary: #0e0e23;

    /* Druhá nejtmavší - záhlaví karet a tabulek */
    --color-bg-secondary: #10122b;

    /* Středně modrá - pozadí karet a kontejnerů */
    --color-bg-tertiary: #252954;

    /* ========================================
       TEXTOVÉ BARVY (3 úrovně)
       ======================================== */

    /* Hlavní šedá - základní text */
    --color-text-primary: #9b9ca7;

    /* Světle šedá - sekundární text, popisky */
    --color-text-secondary: #dee2ec;

    /* Bílá - důležitý text, nadpisy */
    --color-text-white: #ffffff;

    /* ========================================
       PRIMÁRNÍ MODRÁ
       ======================================== */

    /* Primární modrá - aktivní prvky, tlačítka, progrese */
    --color-primary: #4255d4;

    /* Světlejší varianta pro hover efekty */
    --color-primary-light: #5c6bff;

    /* Tmavší varianta pro pressed stavy */
    --color-primary-dark: #3e50c5;

    /* ========================================
       STATUS BARVY
       ======================================== */

    /* Zelená - úspěšné stavy */
    --color-success: #17a98a;
    --color-success-light: #1cc39d;
    --color-success-dark: #13645b;

    /* Červená - chybové stavy */
    --color-danger: #ef415c;
    --color-danger-light: #ff4d6d;
    --color-danger-dark: #d14b69;

    /* ========================================
       AKCENTNÍ ORANŽOVÁ
       ======================================== */

    /* Oranžová - časové značky, badges, speciální prvky */
    --color-accent-orange: #ef8741;
    --color-accent-orange-dark: #9e5924;

    /* ========================================
       UTILITY BARVY
       ======================================== */

    /* Průhledné barvy pro efekty */
    --color-border: rgba(255, 255, 255, 0.1);
    --color-hover: rgba(255, 255, 255, 0.05);
    --color-active: rgba(66, 85, 212, 0.1);

    /* Čekající/disabled stavy */
    --color-muted: #6c757d;

    /* Transparentní */
    --color-transparent: transparent;

    /* ========================================
       GRADIENTOVÉ MIXINY (matematicky odvozené)
       ======================================== */

    /* Výpočet světlejších variant pomocí color-mix */
    --color-bg-tertiary-light: color-mix(in srgb, var(--color-bg-tertiary) 85%, white);
    --color-bg-secondary-light: color-mix(in srgb, var(--color-bg-secondary) 85%, white);
    --color-primary-gradient-light: color-mix(in srgb, var(--color-primary) 80%, white);

    /* Gradienty z jedné základní barvy */
    --gradient-card: radial-gradient(
      circle,
      var(--color-bg-tertiary-light) 0%,
      var(--color-bg-tertiary) 100%
    );

    --gradient-header: radial-gradient(
      circle,
      var(--color-bg-secondary-light) 0%,
      var(--color-bg-secondary) 100%
    );

    /* Primární gradient z jedné barvy */
    --gradient-primary: linear-gradient(
      135deg,
      var(--color-primary-gradient-light) 0%,
      var(--color-primary) 100%
    );

    /* Fallback gradienty pro starší prohlížeče (bez color-mix podpory) */
    @supports not (color: color-mix(in srgb, red, blue)) {
      --gradient-card: radial-gradient(
        circle,
        #2f3462 0%,
        var(--color-bg-tertiary) 100%
      );

      --gradient-header: radial-gradient(
        circle,
        #171a39 0%,
        var(--color-bg-secondary) 100%
      );

      --gradient-primary: linear-gradient(
        135deg,
        #5c6bff 0%,
        var(--color-primary) 100%
      );
    }

    /* ========================================
       KOMPATIBILNÍ ALIASY (pro zpětnou kompatibilitu)
       ======================================== */

    /* Mapování starých názvů na nové */
    --color-background: var(--color-bg-primary);
    --color-dark: var(--color-bg-secondary);
    --color-darker: var(--color-bg-primary);
    --color-light: var(--color-text-secondary);
    --color-lighter: var(--color-text-white);
    --color-accent: var(--color-primary);
    --color-accent-light: var(--color-primary-light);
    --color-accent-dark: var(--color-primary-dark);
    --color-wait: var(--color-muted);

    /* Text aliasy */
    --text-muted: var(--color-muted);
    --text-white: var(--color-text-white);
    --text-black-50: rgba(0, 0, 0, 0.5);
    --text-white-50: rgba(255, 255, 255, 0.5);

    /* Background aliasy */
    --bg-white: var(--color-text-white);
    --bg-transparent: var(--color-transparent);
    --bg-cards-button: var(--color-bg-secondary);
    --bg-badge-warning: var(--color-accent-orange-dark);
    --bg-badge-info: var(--color-bg-secondary);

    /* Form control aliasy */
    --form-control-bg: var(--color-bg-secondary);
    --form-control-focus-bg: var(--color-bg-primary);
    --form-control-focus-border: var(--color-primary);
    --form-control-focus-shadow: 0 0 0 0.2rem rgba(66, 85, 212, 0.25);

    /* Tooltip aliasy */
    --tooltip-bg: #000000;
    --tooltip-color: var(--color-text-white);
    --tooltip-arrow-color: #000000;
    
    /* Spacing */
    --spacing-xxs: 0.25rem;
    --spacing-xs: 0.5rem;
    --spacing-sm: 0.75rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius: 0.375rem;
    --border-radius-lg: 0.5rem;
    --border-radius-pill: 50rem;
    
    /* Box Shadow */
    --box-shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --box-shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    
    /* Z-index */
    --zindex-dropdown: 1000;
    --zindex-sticky: 1020;
    --zindex-fixed: 1030;
    --zindex-modal: 1050;
    --zindex-popover: 1070;
    --zindex-tooltip: 1080;
    
    /* Layout */
    --header-height: 60px;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;
    --footer-height: 60px;
    
    /* Sidebar Specific */
    --sidebar-bg: var(--color-bg-secondary);
    --sidebar-color: var(--color-text-primary);
    --sidebar-hover-color: var(--color-text-secondary);
    --sidebar-active-color: var(--color-primary);
    --sidebar-submenu-bg: rgba(0, 0, 0, 0.2);
    --sidebar-divider: var(--color-border);
    --sidebar-transition: all var(--transition-duration) var(--transition-timing);
    
    /* Header Specific */
    --header-bg: var(--color-bg-secondary);
    --header-color: var(--color-text-primary);
    --header-hover-bg: var(--color-hover);
    
    /* Card Specific */
    --card-bg: var(--color-bg-secondary);
    --card-border-color: var(--color-border);
    --card-cap-bg: rgba(0, 0, 0, 0.03);

    /* Gradientové „mixiny“ */

}
